version: '3.8'

# defaults to csgompire, override with COMPOSE_PROJECT_NAME
name: csgoempire

networks:
  app:
    name: ${CSGOEMPIRE_DOCKER_NETWORK_NAME:-${APP_NAME:-csgoempire}}

services:
  admin-panel:
    container_name: ${APP_NAME:-csgoempire}-core-admin-panel
    image: ${APP_NAME:-csgoempire}/admin-panel
    build:
      context: ./
      dockerfile: ./Dockerfile
    entrypoint: ["sh", "-c", "cd /var/www/html/admin-src && vue-cli-service serve"]
    working_dir: /var/www/html/admin-src
    env_file:
        - dev.env
    environment:
      - VUE_APP_DEV_PROXY_URL=http://${APP_NAME:-csgoempire}.localhost
      - VUE_APP_NAME=${APP_NAME:-csgoempire}
    volumes:
      - ./vue.config.js:/var/www/html/admin-src/vue.config.js:cached
      - ./src:/var/www/html/admin-src/src:cached
      - ./package.json:/var/www/html/admin-src/package.json:cached
      - ./package-lock.json:/var/www/html/admin-src/package-lock.json:cached
    ports:
      - ${EXPOSED_PORT_ADMIN_PANEL:-8082}:8080
    networks:
      app:
        aliases:
          - admin-panel
          - ${APP_NAME:-csgoempire}-admin-panel
