IS_LOCAL_ENVIRONMENT=0

# DB_HOST=csgoempire-database
# DB_USER=csgoempire
# DB_PASSWORD=csgoempire
# DB_PORT=3306
# DB_DATABASE=csgoempire
# DB_CONNECTION_LIMIT=3500

# REDIS_HOST=csgoempire-redis
# REDIS_PORT=6379
# REDIS_HOST_SOCKET=csgoempire-redis-socket
# REDIS_PORT_SOCKET=6379

ROLL_TIMER=15
MIN_BET=0

# How many nodes we start
# NODE_COUNT=3

# Timeout to wait exit before force kill on shutting down. Defaults to 2mins (120000)
# MAX_EXIT_TIMEOUT=120000

# INTERNAL_API_URL=http://csgoempire.backend/api/v2/socket
# INTERNAL_API_TOKEN=random-token-for-internal-socket-api-requests

# DISABLE_PROVABLY_FAIR=true

APP_PORT=2096

EXCEPTIONS_WEBHOOK=
NOTIFICATION_CHANNEL=
LIVE_FEED_TIPS_CHANNEL=

LOGIN_COUNT_MULTIPLIER=1

# Used to verify captcha before tipping
RECAPTCHA_PUBLIC_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
RECAPTCHA_SECRET_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe

# disable/enable based on cluster
MASTER_ENABLED=true
SLAVE_ENABLED=true

# you can enable just one service via SERVICE_ENABLED
# this will ovverride all XXX_ENABLED -variables
# SERVICE_ENABLED=coinflip|roulette|notifications|chat|trade

# CHAT_ENABLED=true
# ROULETTE_ENABLED=true
# COINFLIP_ENABLED=true
# NOTIFICATIONS_ENABLED=true
# MATCHBETTING_ENABLED=true
# CASEBATTLE_ENABLED=true
# GAMEINTEGRATION_ENABLED=true
# TRADE_ENABLED=true

# Use local inventory in local dev
# USE_LOCAL_INVENTORY=true

# Disable inventories. All enabled by default.
# DISABLE_HERMES_INVENTORY=true
# DISABLE_P2P_INVENTORY=true

# Override default urls
# HERMES_INVENTORY_URL=https://url/to/hermes/inventory
# P2P_INVENTORY_URL=https://url/to/p2p/inventory

# The eos mainnet endpoint (without wss:// or https:// or trailing slash)
EOS_API_ENDPOINT=mainnet.eos.dfuse.io
# The API key generated at eos substreams. It should be a "server" type key.
SUBSTREAMS_API_KEY=

# Enable https://github.com/davidmarkclements/0x in dev env
ENABLE_0X=0

# Enable inspector
ENABLE_INSPECTOR=0

# Use all available CPUs and enable node cluster support
# ENABLE_NODE_CLUSTER=true

# Disable/enable standby masters
# DISABLE_STANDBY_MASTER=1

ENABLE_EVENT_LOOP_MONITORING=1

# Case Battles
INTERNAL_API_ACTIVE_BATTLES='/case-battle/active'
ROUND_DELAY=6
