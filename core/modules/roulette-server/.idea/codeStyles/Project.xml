<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="RIGHT_MARGIN" value="100" />
    <JSCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="USE_DOUBLE_QUOTES" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="WhenMultiline" />
      <option name="VAR_DECLARATION_WRAP" value="2" />
      <option name="OBJECT_LITERAL_WRAP" value="2" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="USE_CHAINED_CALLS_GROUP_INDENTS" value="true" />
    </JSCodeStyleSettings>
    <codeStyleSettings language="JSON">
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="RIGHT_MARGIN" value="150" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="2" />
      <option name="BINARY_OPERATION_WRAP" value="1" />
      <option name="TERNARY_OPERATION_WRAP" value="5" />
      <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
      <option name="KEEP_SIMPLE_BLOCKS_IN_ONE_LINE" value="true" />
      <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="5" />
      <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="1" />
      <option name="DOWHILE_BRACE_FORCE" value="1" />
      <option name="WHILE_BRACE_FORCE" value="1" />
      <option name="FOR_BRACE_FORCE" value="1" />
    </codeStyleSettings>
    <codeStyleSettings language="SASS">
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="SCSS">
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>