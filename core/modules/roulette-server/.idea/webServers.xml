<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WebServers">
    <option name="servers">
      <webServer id="c5cd578f-4b05-408d-aab7-80f99b6ae8a4" name="PROD-01" url="http://ec2-18-222-47-75.us-east-2.compute.amazonaws.com">
        <fileTransfer host="ec2-18-222-47-75.us-east-2.compute.amazonaws.com" port="22" privateKey="C:\Users\<USER>\Dropbox\Docs\jzhang_privatekey.ppk" rootFolder="/home/<USER>" accessType="SFTP" keyPair="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" />
          </advancedOptions>
          <option name="port" value="22" />
        </fileTransfer>
      </webServer>
      <webServer id="c1f82236-b75f-4d2f-93ea-31769a7ef93f" name="PROD-02" url="http://ec2-3-130-240-112.us-east-2.compute.amazonaws.com">
        <fileTransfer host="ec2-3-130-240-112.us-east-2.compute.amazonaws.com" port="22" privateKey="C:\Users\<USER>\Dropbox\Docs\jzhang_privatekey.ppk" rootFolder="/home/<USER>" accessType="SFTP" keyPair="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" />
          </advancedOptions>
          <option name="port" value="22" />
        </fileTransfer>
      </webServer>
      <webServer id="9fe3f22b-5e06-40f7-9368-c7b4e62a10b8" name="PROD-03" url="http://ec2-18-189-77-10.us-east-2.compute.amazonaws.com">
        <fileTransfer host="ec2-18-189-77-10.us-east-2.compute.amazonaws.com" port="22" privateKey="C:\Users\<USER>\Dropbox\Docs\jzhang_privatekey.ppk" rootFolder="/home/<USER>" accessType="SFTP" keyPair="true">
          <advancedOptions>
            <advancedOptions dataProtectionLevel="Private" />
          </advancedOptions>
          <option name="port" value="22" />
        </fileTransfer>
      </webServer>
    </option>
  </component>
</project>