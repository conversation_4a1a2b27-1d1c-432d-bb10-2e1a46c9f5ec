{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "vcs", "url": "https://github.com/laravel-shift/laravel-steam-auth.git"}], "require": {"php": "^8.1", "ext-pdo": "*", "ext-bcmath": "*", "ext-openssl": "*", "ext-zlib": "*", "ext-iconv": "*", "ext-msgpack": "*", "ext-newrelic": "*", "laravel/framework": "^10.48.23", "waylaidwanderer/php-rotatingproxymanager": "^1.0", "doctrine/dbal": "^3.1", "guzzlehttp/guzzle": "^7.9.3", "predis/predis": "^2.3.0", "invisnik/laravel-steam-auth": "dev-l10-compatibility#c0eabca1babfeeff0bbd43591e932aaa314c31cc", "submtd/cashaddr-converter": "dev-master#8a02f8955872effea940672b3533a72f0318141e", "spomky-labs/otphp": "^11.0", "lastguest/murmurhash": "^2.0", "fntneves/laravel-transactional-events": "^2.0", "abraham/twitteroauth": "^7.0", "geoip2/geoip2": "^3.0", "league/iso3166": "^4.0", "malkusch/lock": "^2.1", "awobaz/compoships": "^2.1", "laravel/slack-notification-channel": "^3.5.0", "spatie/laravel-query-builder": "^5.0", "laravel-validation-rules/credit-card": "^1.10.0", "web3p/ethereum-tx": "^0.4.0", "doctormckay/steamid": "^1.0", "laravel-notification-channels/telegram": "^5.0", "daursu/laravel-zero-downtime-migration": "^1.1.0", "gghughunishvili/entrust": "dev-master#7a702b38daf3e8b39b99b6879e50d3cbd9ac2fc2", "laravel/helpers": "^1.4", "aws/aws-sdk-php": "^3.342.27", "laravel/horizon": "^5.31.1", "lkaemmerling/laravel-horizon-prometheus-exporter": "^1.4", "spatie/data-transfer-object": "^3.7", "jessarcher/laravel-castable-data-transfer-object": "^2.2", "firebase/php-jwt": "^6.11.1", "genealabs/laravel-pivot-events": "^12.0.0", "rogervila/array-diff-multidimensional": "^2.1", "sebastian/comparator": "^5.0.2", "iksaku/laravel-mass-update": "^1.0", "php-amqplib/php-amqplib": "^3.5", "laravel/socialite": "^5.11", "spatie/laravel-data": "4.7", "phpoffice/phpspreadsheet": "^4.0", "league/flysystem-aws-s3-v3": "^3.29.0", "friendsofcat/opensearch-client": "^2.0", "laravel/scout": "^10.14.1", "friendsofcat/opensearch-scout-driver": "^2.1", "friendsofcat/opensearch-scout-driver-plus": "^2.1", "laravel/tinker": "^2.10"}, "require-dev": {"filp/whoops": "^2.18.0", "fzaninotto/faker": "^1.9.2", "phpunit/phpunit": "^10.0", "symfony/css-selector": "3.1.*", "symfony/dom-crawler": "3.1.*", "orangehill/iseed": "^3.1.1", "squizlabs/php_codesniffer": "^3.12.2", "mockery/mockery": "^1.4", "larastan/larastan": "^2.0", "jetbrains/phpstorm-attributes": "^1.2", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.4", "barryvdh/laravel-ide-helper": "^3.1", "phpstan/phpstan": "^1.11"}, "autoload": {"psr-4": {"App\\": "app/", "Tests\\": "tests/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"classmap": ["tests/TestCase.php"]}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "php artisan ide-helper:generate", "php artisan ide-helper:meta", "@php artisan horizon:publish --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "phpcs": "cd .. && vendor/squizlabs/php_codesniffer/bin/phpcs --standard=phpcs.xml laravel", "phpcbf": "cd .. && vendor/squizlabs/php_codesniffer/bin/phpcbf --standard=phpcs.xml laravel", "phpstan": "./vendor/bin/phpstan -v analyse --memory-limit=2G"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "platform": {"php": "8.1.17"}, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": false}}}