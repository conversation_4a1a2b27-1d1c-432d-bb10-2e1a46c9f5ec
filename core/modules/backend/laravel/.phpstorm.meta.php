<?php
/* @noinspection ALL */
// @formatter:off
// phpcs:ignoreFile

namespace PHPSTORM_META {

   /**
    * PhpStorm Meta file, to provide autocomplete information for PhpStorm
    *
    * <AUTHOR> vd. <PERSON>l <<EMAIL>>
    * @see https://github.com/barryvdh/laravel-ide-helper
    */
    override(new \Illuminate\Contracts\Container\Container, map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\Illuminate\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::get(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::make(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\App::get(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\App::make(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\App::makeWith(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\app(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\resolve(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));
    override(\Psr\Container\ContainerInterface::get(0), map([
        '' => '@',
            'App\Http\Request' => \App\Http\Request::class,
            'App\Money\CurrencyExchangeCalculator' => \App\Money\CurrencyExchangeCalculator::class,
            'App\Services\DomainService' => \App\Services\DomainService::class,
            'App\Services\SocketHelperService' => \App\Services\SocketHelperService::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClient' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'App\Trading\Services\OpenSearch\LiveItemOpenSearchClientInterface' => \App\Trading\Services\OpenSearch\LiveItemOpenSearchClient::class,
            'Illuminate\Auth\Console\ClearResetsCommand' => \Illuminate\Auth\Console\ClearResetsCommand::class,
            'Illuminate\Broadcasting\BroadcastManager' => \Illuminate\Broadcasting\BroadcastManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\DatabaseBatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Bus\Dispatcher' => \Illuminate\Bus\Dispatcher::class,
            'Illuminate\Cache\Console\ForgetCommand' => \Illuminate\Cache\Console\ForgetCommand::class,
            'Illuminate\Cache\Console\PruneStaleTagsCommand' => \Illuminate\Cache\Console\PruneStaleTagsCommand::class,
            'Illuminate\Cache\RateLimiter' => \Illuminate\Cache\RateLimiter::class,
            'Illuminate\Console\Scheduling\ScheduleClearCacheCommand' => \Illuminate\Console\Scheduling\ScheduleClearCacheCommand::class,
            'Illuminate\Console\Scheduling\ScheduleFinishCommand' => \Illuminate\Console\Scheduling\ScheduleFinishCommand::class,
            'Illuminate\Console\Scheduling\ScheduleInterruptCommand' => \Illuminate\Console\Scheduling\ScheduleInterruptCommand::class,
            'Illuminate\Console\Scheduling\ScheduleListCommand' => \Illuminate\Console\Scheduling\ScheduleListCommand::class,
            'Illuminate\Console\Scheduling\ScheduleRunCommand' => \Illuminate\Console\Scheduling\ScheduleRunCommand::class,
            'Illuminate\Console\Scheduling\ScheduleTestCommand' => \Illuminate\Console\Scheduling\ScheduleTestCommand::class,
            'Illuminate\Console\Scheduling\ScheduleWorkCommand' => \Illuminate\Console\Scheduling\ScheduleWorkCommand::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\LogBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \App\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \App\Exceptions\Handler::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Database\Console\DbCommand' => \Illuminate\Database\Console\DbCommand::class,
            'Illuminate\Database\Console\DumpCommand' => \Illuminate\Database\Console\DumpCommand::class,
            'Illuminate\Database\Console\Migrations\FreshCommand' => \Illuminate\Database\Console\Migrations\FreshCommand::class,
            'Illuminate\Database\Console\Migrations\InstallCommand' => \Illuminate\Database\Console\Migrations\InstallCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateCommand' => \App\Console\Migration\MigrateCommand::class,
            'Illuminate\Database\Console\Migrations\MigrateMakeCommand' => \Illuminate\Database\Console\Migrations\MigrateMakeCommand::class,
            'Illuminate\Database\Console\Migrations\RefreshCommand' => \Illuminate\Database\Console\Migrations\RefreshCommand::class,
            'Illuminate\Database\Console\Migrations\ResetCommand' => \Illuminate\Database\Console\Migrations\ResetCommand::class,
            'Illuminate\Database\Console\Migrations\RollbackCommand' => \App\Console\Migration\RollbackCommand::class,
            'Illuminate\Database\Console\Migrations\StatusCommand' => \Illuminate\Database\Console\Migrations\StatusCommand::class,
            'Illuminate\Database\Console\PruneCommand' => \Illuminate\Database\Console\PruneCommand::class,
            'Illuminate\Database\Console\Seeds\SeedCommand' => \App\Console\Migration\SeedCommand::class,
            'Illuminate\Database\Console\WipeCommand' => \Illuminate\Database\Console\WipeCommand::class,
            'Illuminate\Foundation\Console\ChannelListCommand' => \Illuminate\Foundation\Console\ChannelListCommand::class,
            'Illuminate\Foundation\Console\ClearCompiledCommand' => \Illuminate\Foundation\Console\ClearCompiledCommand::class,
            'Illuminate\Foundation\Console\ConfigShowCommand' => \Illuminate\Foundation\Console\ConfigShowCommand::class,
            'Illuminate\Foundation\Console\DocsCommand' => \Illuminate\Foundation\Console\DocsCommand::class,
            'Illuminate\Foundation\Console\DownCommand' => \Illuminate\Foundation\Console\DownCommand::class,
            'Illuminate\Foundation\Console\EnvironmentCommand' => \Illuminate\Foundation\Console\EnvironmentCommand::class,
            'Illuminate\Foundation\Console\EventCacheCommand' => \Illuminate\Foundation\Console\EventCacheCommand::class,
            'Illuminate\Foundation\Console\EventGenerateCommand' => \Illuminate\Foundation\Console\EventGenerateCommand::class,
            'Illuminate\Foundation\Console\EventListCommand' => \Illuminate\Foundation\Console\EventListCommand::class,
            'Illuminate\Foundation\Console\KeyGenerateCommand' => \Illuminate\Foundation\Console\KeyGenerateCommand::class,
            'Illuminate\Foundation\Console\LangPublishCommand' => \Illuminate\Foundation\Console\LangPublishCommand::class,
            'Illuminate\Foundation\Console\OptimizeClearCommand' => \Illuminate\Foundation\Console\OptimizeClearCommand::class,
            'Illuminate\Foundation\Console\OptimizeCommand' => \Illuminate\Foundation\Console\OptimizeCommand::class,
            'Illuminate\Foundation\Console\PackageDiscoverCommand' => \Illuminate\Foundation\Console\PackageDiscoverCommand::class,
            'Illuminate\Foundation\Console\ServeCommand' => \Illuminate\Foundation\Console\ServeCommand::class,
            'Illuminate\Foundation\Console\StorageLinkCommand' => \Illuminate\Foundation\Console\StorageLinkCommand::class,
            'Illuminate\Foundation\Console\StorageUnlinkCommand' => \Illuminate\Foundation\Console\StorageUnlinkCommand::class,
            'Illuminate\Foundation\Console\StubPublishCommand' => \Illuminate\Foundation\Console\StubPublishCommand::class,
            'Illuminate\Foundation\Console\UpCommand' => \Illuminate\Foundation\Console\UpCommand::class,
            'Illuminate\Foundation\Console\ViewCacheCommand' => \Illuminate\Foundation\Console\ViewCacheCommand::class,
            'Illuminate\Foundation\MaintenanceModeManager' => \Illuminate\Foundation\MaintenanceModeManager::class,
            'Illuminate\Foundation\Mix' => \Illuminate\Foundation\Mix::class,
            'Illuminate\Foundation\PackageManifest' => \Illuminate\Foundation\PackageManifest::class,
            'Illuminate\Foundation\Vite' => \Illuminate\Foundation\Vite::class,
            'Illuminate\Notifications\ChannelManager' => \Illuminate\Notifications\ChannelManager::class,
            'Illuminate\Queue\Console\ClearCommand' => \Illuminate\Queue\Console\ClearCommand::class,
            'Illuminate\Queue\Console\FlushFailedCommand' => \Illuminate\Queue\Console\FlushFailedCommand::class,
            'Illuminate\Queue\Console\ForgetFailedCommand' => \Illuminate\Queue\Console\ForgetFailedCommand::class,
            'Illuminate\Queue\Console\ListFailedCommand' => \Illuminate\Queue\Console\ListFailedCommand::class,
            'Illuminate\Queue\Console\ListenCommand' => \Illuminate\Queue\Console\ListenCommand::class,
            'Illuminate\Queue\Console\PruneBatchesCommand' => \Illuminate\Queue\Console\PruneBatchesCommand::class,
            'Illuminate\Queue\Console\PruneFailedJobsCommand' => \Illuminate\Queue\Console\PruneFailedJobsCommand::class,
            'Illuminate\Queue\Console\RestartCommand' => \Illuminate\Queue\Console\RestartCommand::class,
            'Illuminate\Queue\Console\RetryBatchCommand' => \Illuminate\Queue\Console\RetryBatchCommand::class,
            'Illuminate\Queue\Console\RetryCommand' => \Illuminate\Queue\Console\RetryCommand::class,
            'Illuminate\Queue\Console\WorkCommand' => \Illuminate\Queue\Console\WorkCommand::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Illuminate\Testing\ParallelTesting' => \Illuminate\Testing\ParallelTesting::class,
            'Laravel\Horizon\Console\WorkCommand' => \Laravel\Horizon\Console\WorkCommand::class,
            'Laravel\Horizon\Listeners\TrimFailedJobs' => \Laravel\Horizon\Listeners\TrimFailedJobs::class,
            'Laravel\Horizon\Listeners\TrimMonitoredJobs' => \Laravel\Horizon\Listeners\TrimMonitoredJobs::class,
            'Laravel\Horizon\Listeners\TrimRecentJobs' => \Laravel\Horizon\Listeners\TrimRecentJobs::class,
            'Laravel\Horizon\Stopwatch' => \Laravel\Horizon\Stopwatch::class,
            'Laravel\Scout\EngineManager' => \Laravel\Scout\EngineManager::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'NotificationChannels\Telegram\Telegram' => \NotificationChannels\Telegram\Telegram::class,
            'OpenSearch\Laravel\Client\ClientBuilderInterface' => \OpenSearch\Laravel\Client\ClientBuilder::class,
            'OpenSearch\ScoutDriverPlus\Factories\RoutingFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\RoutingFactory::class,
            'OpenSearch\ScoutDriver\Engine' => \OpenSearch\ScoutDriverPlus\Engine::class,
            'OpenSearch\ScoutDriver\Factories\DocumentFactoryInterface' => \OpenSearch\ScoutDriverPlus\Factories\DocumentFactory::class,
            'OpenSearch\ScoutDriver\Factories\ModelFactoryInterface' => \OpenSearch\ScoutDriver\Factories\ModelFactory::class,
            'OpenSearch\ScoutDriver\Factories\SearchParametersFactoryInterface' => \OpenSearch\ScoutDriver\Factories\SearchParametersFactory::class,
            'Spatie\LaravelData\Support\Caching\DataStructureCache' => \Spatie\LaravelData\Support\Caching\DataStructureCache::class,
            'Spatie\StructureDiscoverer\Discover' => \Spatie\StructureDiscoverer\Discover::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'cashaddr-converter' => \Submtd\CashaddrConverter\CashaddrConverter::class,
            'command.entrust.migration' => \Zizaco\Entrust\MigrationCommand::class,
            'command.ide-helper.eloquent' => \Barryvdh\LaravelIdeHelper\Console\EloquentCommand::class,
            'command.ide-helper.generate' => \Barryvdh\LaravelIdeHelper\Console\GeneratorCommand::class,
            'command.ide-helper.meta' => \Barryvdh\LaravelIdeHelper\Console\MetaCommand::class,
            'command.ide-helper.models' => \Barryvdh\LaravelIdeHelper\Console\ModelsCommand::class,
            'command.iseed' => \Orangehill\Iseed\IseedCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \App\Database\MySqlConnection::class,
            'db.connector.gh-ost' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.connector.pt-online-schema-change' => \Illuminate\Database\Connectors\MySqlConnector::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\MySqlBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Illuminate\Queue\SyncQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
        ]));


    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::mock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::partialMock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::instance(0), type(1));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::spy(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Support\Arr::add(0), type(0));
    override(\Illuminate\Support\Arr::except(0), type(0));
    override(\Illuminate\Support\Arr::first(0), elementType(0));
    override(\Illuminate\Support\Arr::last(0), elementType(0));
    override(\Illuminate\Support\Arr::get(0), elementType(0));
    override(\Illuminate\Support\Arr::only(0), type(0));
    override(\Illuminate\Support\Arr::prepend(0), type(0));
    override(\Illuminate\Support\Arr::pull(0), elementType(0));
    override(\Illuminate\Support\Arr::set(0), type(0));
    override(\Illuminate\Support\Arr::shuffle(0), type(0));
    override(\Illuminate\Support\Arr::sort(0), type(0));
    override(\Illuminate\Support\Arr::sortRecursive(0), type(0));
    override(\Illuminate\Support\Arr::where(0), type(0));
    override(\array_add(0), type(0));
    override(\array_except(0), type(0));
    override(\array_first(0), elementType(0));
    override(\array_last(0), elementType(0));
    override(\array_get(0), elementType(0));
    override(\array_only(0), type(0));
    override(\array_prepend(0), type(0));
    override(\array_pull(0), elementType(0));
    override(\array_set(0), type(0));
    override(\array_sort(0), type(0));
    override(\array_sort_recursive(0), type(0));
    override(\array_where(0), type(0));
    override(\head(0), elementType(0));
    override(\last(0), elementType(0));
    override(\with(0), type(0));
    override(\tap(0), type(0));
    override(\optional(0), type(0));

}
